#!/usr/bin/env python3
"""
Script na generovanie threshold logov podobných threshold_logs_sample.txt
Analyzuje entry signály a loguje kedy prekročia dynamické thresholdy
"""

import pandas as pd
import numpy as np
import logging
import json
from datetime import datetime, timezone
import argparse
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
log = logging.getLogger(__name__)

class DynamicThresholdAnalyzer:
    """Analyzuje entry signály a generuje threshold logy"""
    
    def __init__(self, config_path="strategyConfig_scalp_1s.json"):
        """Initialize analyzer with config"""
        with open(config_path, 'r') as f:
            self.config = json.load(f)
        
        # Base thresholds from config
        trade_params = self.config.get('tradeParams', {})
        self.base_long_threshold = trade_params.get('longEntryThreshold', 0.6)
        self.base_short_threshold = trade_params.get('shortEntryThreshold', 0.6)
        self.base_exit_threshold = trade_params.get('exitActionThreshold', 0.6)
        
        # Dynamic threshold settings
        self.enable_dynamic = trade_params.get('enableDynamicThresholds', True)
        self.volatility_factor = trade_params.get('volatilityAdjustmentFactor', 0.15)
        
        log.info(f"Entry thresholds - Long: {self.base_long_threshold}, Short: {self.base_short_threshold}, Exit: {self.base_exit_threshold}")
        log.info(f"Dynamic thresholds enabled: {self.enable_dynamic}")
    
    def calculate_dynamic_thresholds(self, current_atr, avg_atr, base_threshold):
        """Calculate dynamic threshold based on volatility"""
        if not self.enable_dynamic or avg_atr == 0:
            return base_threshold
        
        # Volatility ratio
        volatility_ratio = current_atr / avg_atr
        
        # Adjust threshold based on volatility
        # Higher volatility = lower threshold (easier entry)
        # Lower volatility = higher threshold (harder entry)
        if volatility_ratio > 1.2:  # High volatility
            adjustment = -self.volatility_factor * (volatility_ratio - 1.0)
        elif volatility_ratio < 0.8:  # Low volatility
            adjustment = self.volatility_factor * (1.0 - volatility_ratio)
        else:  # Normal volatility
            adjustment = 0.0
        
        dynamic_threshold = base_threshold + adjustment
        return max(0.3, min(0.9, dynamic_threshold))  # Clamp between 0.3 and 0.9
    
    def analyze_data(self, parquet_file, start_date=None, end_date=None):
        """Analyze parquet data and generate threshold logs"""
        
        log.info(f"Loading data from {parquet_file}")
        df = pd.read_parquet(parquet_file)
        
        # Convert timestamp to datetime
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
        
        # Filter by date range if provided
        if start_date:
            start_dt = pd.to_datetime(start_date, utc=True)
            df = df[df['datetime'] >= start_dt]
        
        if end_date:
            end_dt = pd.to_datetime(end_date, utc=True) + pd.Timedelta(days=1)
            df = df[df['datetime'] < end_dt]
        
        log.info(f"Analyzing {len(df)} data points from {df['datetime'].min()} to {df['datetime'].max()}")
        
        # Check required columns
        required_cols = ['entry_sig', 'atr']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            log.error(f"Missing required columns: {missing_cols}")
            log.info(f"Available columns: {list(df.columns)}")
            return
        
        # Calculate rolling average ATR for dynamic thresholds
        df['avg_atr'] = df['atr'].rolling(window=20, min_periods=1).mean()
        
        threshold_exceeded_count = 0
        
        # Analyze each row
        for idx, row in df.iterrows():
            entry_sig = row['entry_sig']
            current_atr = row['atr']
            avg_atr = row['avg_atr']
            timestamp = row['datetime']
            
            # Calculate dynamic thresholds
            dynamic_long_threshold = self.calculate_dynamic_thresholds(
                current_atr, avg_atr, self.base_long_threshold
            )
            dynamic_short_threshold = self.calculate_dynamic_thresholds(
                current_atr, avg_atr, self.base_short_threshold
            )
            
            # Check for threshold exceedance
            long_exceeded = entry_sig > dynamic_long_threshold
            short_exceeded = entry_sig < -dynamic_short_threshold
            
            if long_exceeded:
                log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                        f"LONG ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} > {dynamic_long_threshold:.12f}")
                threshold_exceeded_count += 1
            
            elif short_exceeded:
                log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                        f"SHORT ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} < -{dynamic_short_threshold:.12f}")
                threshold_exceeded_count += 1
        
        log.info(f"Analysis complete. Found {threshold_exceeded_count} threshold exceedances.")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Generate threshold logs from parquet data')
    parser.add_argument('--data', required=True, help='Path to parquet file')
    parser.add_argument('--config', default='strategyConfig_scalp_1s.json', help='Config file path')
    parser.add_argument('--start', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end', help='End date (YYYY-MM-DD)')
    parser.add_argument('--output', help='Output log file (optional)')
    
    args = parser.parse_args()
    
    # Setup output logging if specified
    if args.output:
        file_handler = logging.FileHandler(args.output)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', 
                                                   datefmt='%Y-%m-%d %H:%M:%S'))
        log.addHandler(file_handler)
    
    # Check if data file exists
    if not Path(args.data).exists():
        log.error(f"Data file not found: {args.data}")
        return
    
    # Check if config file exists
    if not Path(args.config).exists():
        log.error(f"Config file not found: {args.config}")
        return
    
    # Create analyzer and run analysis
    analyzer = DynamicThresholdAnalyzer(args.config)
    analyzer.analyze_data(args.data, args.start, args.end)

if __name__ == "__main__":
    main()
