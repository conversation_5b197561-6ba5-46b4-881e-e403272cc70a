#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON>ý analyzátor threshold logov
Analyzuje threshold_logs a zobrazuje štatistiky
"""

import re
from datetime import datetime
from collections import defaultdict, Counter
import sys

def parse_threshold_logs(log_file):
    """Parse threshold logs and extract statistics"""
    
    # Patterns for different log types
    patterns = {
        'long_entry': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2} \(\d{4}-\d{2}-\d{2}\): LONG ENTRY THRESHOLD EXCEEDED: ([\d\.-]+) > ([\d\.-]+)',
        'short_entry': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2} \(\d{4}-\d{2}-\d{2}\): SHORT ENTRY THRESHOLD EXCEEDED: ([\d\.-]+) < -([\d\.-]+)',
        'exit': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2} \(\d{4}-\d{2}-\d{2}\): EXIT THRESHOLD EXCEEDED: ([\d\.-]+) \(thresh ([\d\.-]+)\)',
        'dynamic_debug': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2}: Dynamic thresholds: Long=([\d\.-]+), Short=([\d\.-]+), Exit=([\d\.-]+) \(ATR: ([\d\.-]+), AvgATR: ([\d\.-]+)\)'
    }
    
    # Read log file
    with open(log_file, 'r') as f:
        content = f.read()
    
    # Parse data
    long_entries = []
    short_entries = []
    exits = []
    dynamic_thresholds = []
    
    # Parse long entries
    for match in re.finditer(patterns['long_entry'], content):
        timestamp_str, signal, threshold = match.groups()
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        long_entries.append({
            'timestamp': timestamp,
            'signal': float(signal),
            'threshold': float(threshold),
            'excess': float(signal) - float(threshold)
        })
    
    # Parse short entries
    for match in re.finditer(patterns['short_entry'], content):
        timestamp_str, signal, threshold = match.groups()
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        short_entries.append({
            'timestamp': timestamp,
            'signal': float(signal),
            'threshold': float(threshold),
            'excess': abs(float(signal)) - float(threshold)
        })
    
    # Parse exits
    for match in re.finditer(patterns['exit'], content):
        timestamp_str, signal, threshold = match.groups()
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        exits.append({
            'timestamp': timestamp,
            'signal': float(signal),
            'threshold': float(threshold)
        })
    
    # Parse dynamic threshold debug info
    for match in re.finditer(patterns['dynamic_debug'], content):
        timestamp_str, long_thr, short_thr, exit_thr, atr, avg_atr = match.groups()
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        dynamic_thresholds.append({
            'timestamp': timestamp,
            'long_threshold': float(long_thr),
            'short_threshold': float(short_thr),
            'exit_threshold': float(exit_thr),
            'atr': float(atr),
            'avg_atr': float(avg_atr),
            'volatility_ratio': float(atr) / float(avg_atr) if float(avg_atr) > 0 else 1.0
        })
    
    return {
        'long_entries': long_entries,
        'short_entries': short_entries,
        'exits': exits,
        'dynamic_thresholds': dynamic_thresholds
    }

def analyze_threshold_data(data):
    """Analyze parsed threshold data and print statistics"""
    
    long_entries = data['long_entries']
    short_entries = data['short_entries']
    exits = data['exits']
    dynamic_thresholds = data['dynamic_thresholds']
    
    print("=" * 60)
    print("📊 THRESHOLD LOG ANALYSIS")
    print("=" * 60)
    
    # Basic counts
    print(f"\n📈 ENTRY SIGNALS:")
    print(f"  • Long entries: {len(long_entries)}")
    print(f"  • Short entries: {len(short_entries)}")
    print(f"  • Total entries: {len(long_entries) + len(short_entries)}")
    print(f"  • Exit signals: {len(exits)}")
    
    if dynamic_thresholds:
        print(f"  • Dynamic threshold updates: {len(dynamic_thresholds)}")
    
    # Time analysis
    all_entries = long_entries + short_entries
    if all_entries:
        timestamps = [entry['timestamp'] for entry in all_entries]
        start_time = min(timestamps)
        end_time = max(timestamps)
        duration = end_time - start_time
        
        print(f"\n⏰ TIME ANALYSIS:")
        print(f"  • Start time: {start_time}")
        print(f"  • End time: {end_time}")
        print(f"  • Duration: {duration}")
        print(f"  • Entries per hour: {len(all_entries) / (duration.total_seconds() / 3600):.1f}")
    
    # Signal strength analysis
    if long_entries:
        long_signals = [entry['signal'] for entry in long_entries]
        long_thresholds = [entry['threshold'] for entry in long_entries]
        long_excesses = [entry['excess'] for entry in long_entries]
        
        print(f"\n📊 LONG ENTRY ANALYSIS:")
        print(f"  • Signal range: {min(long_signals):.4f} to {max(long_signals):.4f}")
        print(f"  • Average signal: {sum(long_signals)/len(long_signals):.4f}")
        print(f"  • Threshold range: {min(long_thresholds):.4f} to {max(long_thresholds):.4f}")
        print(f"  • Average threshold: {sum(long_thresholds)/len(long_thresholds):.4f}")
        print(f"  • Average excess: {sum(long_excesses)/len(long_excesses):.4f}")
        print(f"  • Max excess: {max(long_excesses):.4f}")
    
    if short_entries:
        short_signals = [abs(entry['signal']) for entry in short_entries]
        short_thresholds = [entry['threshold'] for entry in short_entries]
        short_excesses = [entry['excess'] for entry in short_entries]
        
        print(f"\n📊 SHORT ENTRY ANALYSIS:")
        print(f"  • Signal range: {min(short_signals):.4f} to {max(short_signals):.4f}")
        print(f"  • Average signal: {sum(short_signals)/len(short_signals):.4f}")
        print(f"  • Threshold range: {min(short_thresholds):.4f} to {max(short_thresholds):.4f}")
        print(f"  • Average threshold: {sum(short_thresholds)/len(short_thresholds):.4f}")
        print(f"  • Average excess: {sum(short_excesses)/len(short_excesses):.4f}")
        print(f"  • Max excess: {max(short_excesses):.4f}")
    
    # Dynamic threshold analysis
    if dynamic_thresholds:
        long_thrs = [dt['long_threshold'] for dt in dynamic_thresholds]
        short_thrs = [dt['short_threshold'] for dt in dynamic_thresholds]
        vol_ratios = [dt['volatility_ratio'] for dt in dynamic_thresholds]
        
        print(f"\n🎯 DYNAMIC THRESHOLD ANALYSIS:")
        print(f"  • Long threshold range: {min(long_thrs):.4f} to {max(long_thrs):.4f}")
        print(f"  • Short threshold range: {min(short_thrs):.4f} to {max(short_thrs):.4f}")
        print(f"  • Volatility ratio range: {min(vol_ratios):.2f} to {max(vol_ratios):.2f}")
        print(f"  • Average volatility ratio: {sum(vol_ratios)/len(vol_ratios):.2f}")
    
    # Hourly distribution
    if all_entries:
        hourly_counts = defaultdict(int)
        for entry in all_entries:
            hour = entry['timestamp'].hour
            hourly_counts[hour] += 1
        
        print(f"\n⏰ HOURLY DISTRIBUTION:")
        for hour in sorted(hourly_counts.keys()):
            count = hourly_counts[hour]
            bar = "█" * (count // max(1, max(hourly_counts.values()) // 20))
            print(f"  • {hour:02d}:00 - {count:3d} entries {bar}")
    
    # Recent entries (last 10)
    if all_entries:
        recent_entries = sorted(all_entries, key=lambda x: x['timestamp'])[-10:]
        print(f"\n🕐 RECENT ENTRIES (last 10):")
        for entry in recent_entries:
            entry_type = "LONG" if entry['signal'] > 0 else "SHORT"
            print(f"  • {entry['timestamp']} - {entry_type}: {entry['signal']:.4f} > {entry['threshold']:.4f}")

def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python simple_threshold_analyzer.py <log_file>")
        print("Example: python simple_threshold_analyzer.py my_threshold_logs.txt")
        sys.exit(1)
    
    log_file = sys.argv[1]
    
    try:
        print(f"🔍 Analyzing threshold logs from: {log_file}")
        data = parse_threshold_logs(log_file)
        analyze_threshold_data(data)
        
    except FileNotFoundError:
        print(f"❌ Error: File '{log_file}' not found")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error analyzing logs: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
