#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON> skript na generovanie threshold logov
Použitie: python quick_threshold_logs.py
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from pathlib import Path

# Setup logging to match the sample format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
log = logging.getLogger(__name__)

def generate_threshold_logs():
    """Generate threshold logs similar to threshold_logs_sample.txt"""
    
    # Configuration
    base_long_threshold = 0.6
    base_short_threshold = 0.6
    base_exit_threshold = 0.6
    enable_dynamic = True
    volatility_factor = 0.15
    
    # Log initial configuration
    log.info(f"Entry thresholds - Long: {base_long_threshold}, Short: {base_short_threshold}, Exit: {base_exit_threshold}")
    log.info(f"Dynamic thresholds enabled: {enable_dynamic}")
    
    # Try to load data from common locations
    data_paths = [
        "parquet_processed/XRPUSDC/1s/2025-01-01.parquet",
        "parquet_processed/XRPUSDC/1s/2025-01-02.parquet",
        "parquet_processed/XRPUSDC/1s/2025-07-01.parquet",
        "/Users/<USER>/Projects/scalpel_new/parquet_processed/XRPUSDC/1s/2025-01-01.parquet"
    ]
    
    df = None
    for data_path in data_paths:
        if Path(data_path).exists():
            log.info(f"Loading data from {data_path}")
            df = pd.read_parquet(data_path)
            break
    
    if df is None:
        log.error("No data file found. Please ensure parquet data exists.")
        log.info("Expected locations:")
        for path in data_paths:
            log.info(f"  - {path}")
        return
    
    # Convert timestamp to datetime
    df['datetime'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    
    # Check required columns
    if 'entry_sig' not in df.columns:
        log.error("Column 'entry_sig' not found in data")
        log.info(f"Available columns: {list(df.columns)}")
        return
    
    if 'atr' not in df.columns:
        log.warning("Column 'atr' not found, using synthetic ATR")
        # Create synthetic ATR based on price volatility
        df['atr'] = df['close'].rolling(window=14).std() * 2
    
    # Calculate rolling average ATR
    df['avg_atr'] = df['atr'].rolling(window=20, min_periods=1).mean()
    
    log.info(f"Analyzing {len(df)} data points")
    
    threshold_count = 0
    
    # Sample every N rows to avoid too many logs (similar to original sample)
    sample_interval = max(1, len(df) // 1000)  # Sample ~1000 points
    
    for idx in range(0, len(df), sample_interval):
        row = df.iloc[idx]
        
        entry_sig = row['entry_sig']
        current_atr = row['atr']
        avg_atr = row['avg_atr']
        timestamp = row['datetime']
        
        # Skip if ATR is NaN
        if pd.isna(current_atr) or pd.isna(avg_atr) or avg_atr == 0:
            continue
        
        # Calculate dynamic thresholds
        volatility_ratio = current_atr / avg_atr
        
        # Adjust thresholds based on volatility
        if volatility_ratio > 1.2:  # High volatility - easier entry
            long_adjustment = -volatility_factor * (volatility_ratio - 1.0)
            short_adjustment = -volatility_factor * (volatility_ratio - 1.0)
        elif volatility_ratio < 0.8:  # Low volatility - harder entry
            long_adjustment = volatility_factor * (1.0 - volatility_ratio)
            short_adjustment = volatility_factor * (1.0 - volatility_ratio)
        else:  # Normal volatility
            long_adjustment = 0.0
            short_adjustment = 0.0
        
        dynamic_long_threshold = max(0.3, min(0.9, base_long_threshold + long_adjustment))
        dynamic_short_threshold = max(0.3, min(0.9, base_short_threshold + short_adjustment))
        
        # Check for threshold exceedance
        long_exceeded = entry_sig > dynamic_long_threshold
        short_exceeded = entry_sig < -dynamic_short_threshold
        
        if long_exceeded:
            log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                    f"LONG ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} > {dynamic_long_threshold:.12f}")
            threshold_count += 1
        
        elif short_exceeded:
            log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                    f"SHORT ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} < -{dynamic_short_threshold:.12f}")
            threshold_count += 1
    
    log.info(f"Analysis complete. Found {threshold_count} threshold exceedances.")

if __name__ == "__main__":
    generate_threshold_logs()
