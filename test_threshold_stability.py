#!/usr/bin/env python3
"""
Test skript na overenie stability entry sign<PERSON><PERSON> po oprave VecNormalize a orderbook drift problémov.
"""

import sys
import json
import logging
from pathlib import Path
from datetime import datetime, timezone

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
log = logging.getLogger(__name__)

def test_simulation_stability():
    """Test stability of entry signals after fixes"""

    # Test if we can at least import the basic functions
    try:
        # Test basic imports first
        import pandas as pd
        import numpy as np
        log.info("✅ Basic dependencies (pandas, numpy) available")

        # Test if we can import the normalization function
        import sys
        sys.path.append('.')

        # Try to import just the functions we modified
        try:
            from simulate_trading_new import apply_orderbook_volume_normalization
            log.info("✅ Orderbook normalization function imported successfully")
        except ImportError as e:
            log.warning(f"⚠️  Cannot import orderbook normalization: {e}")

        # Test the normalization function with dummy data
        test_df = pd.DataFrame({
            'ob_bid_vol_l1': [100, 200, 150, 300, 250],
            'ob_ask_vol_l1': [120, 180, 160, 280, 240],
            'other_feature': [1, 2, 3, 4, 5]
        })

        normalized_df, stats = apply_orderbook_volume_normalization(test_df)
        log.info("✅ Orderbook normalization function works correctly")
        log.info(f"   Original range: ob_bid_vol_l1 [{test_df['ob_bid_vol_l1'].min():.1f}, {test_df['ob_bid_vol_l1'].max():.1f}]")
        log.info(f"   Normalized range: ob_bid_vol_l1 [{normalized_df['ob_bid_vol_l1'].min():.3f}, {normalized_df['ob_bid_vol_l1'].max():.3f}]")

        return True

    except ImportError as e:
        log.error(f"Cannot import required dependencies: {e}")
        log.info("💡 Try installing missing dependencies:")
        log.info("   pip install pandas numpy pyarrow")
        return False

        # Test config loading
        config_path = "strategyConfig_scalp_1s.json"
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            log.info("✅ Configuration file loaded successfully")

            # Check key configuration values
            trade_params = config.get('tradeParams', {})
            long_thresh = trade_params.get('longEntryThreshold', 'not set')
            short_thresh = trade_params.get('shortEntryThreshold', 'not set')
            dynamic_enabled = trade_params.get('enableDynamicThresholds', 'not set')

            log.info(f"   Current thresholds: Long={long_thresh}, Short={short_thresh}")
            log.info(f"   Dynamic thresholds: {dynamic_enabled}")

            if long_thresh == 0.99 and short_thresh == 0.99:
                log.warning("⚠️  Very high thresholds (0.99) - may result in very few trades")
                log.info("💡 Consider lowering to 0.6-0.7 for more realistic testing")

        except FileNotFoundError:
            log.error(f"❌ Config file {config_path} not found")
            return False
        except json.JSONDecodeError as e:
            log.error(f"❌ Invalid JSON in config file: {e}")
            return False

        return True

    except Exception as e:
        log.error(f"❌ Test failed with error: {e}")
        import traceback
        log.error(traceback.format_exc())
        return False

def main():
    """Main test function"""
    log.info("🔧 Testing threshold stability fixes...")
    log.info("   1. VecNormalize loading improvements")
    log.info("   2. Orderbook volume normalization")
    log.info("   3. Enhanced diagnostics")
    
    success = test_simulation_stability()
    
    if success:
        log.info("✅ All tests passed! The fixes should help stabilize entry signals.")
        log.info("\n📋 Next steps:")
        log.info("   1. Run full simulation with: python simulate_trading_new.py")
        log.info("   2. Check logs for VecNormalize loading status")
        log.info("   3. Monitor entry signal statistics for stability")
        log.info("   4. Look for warnings about signal drift or normalization issues")
    else:
        log.error("❌ Tests failed. Please check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
