# Oprava kolísania prahov (Threshold Stability Fixes)

## Identifikované problémy

### 1. VecNormalize problém
- **Problém**: VecNormalize súbor sa nenačítaval správne, čo spôsobovalo drift v distribúcii entry sign<PERSON>lov
- **Prejav**: Entry signály sa po nahratí modelu posunuli, prahy začali "skákať"
- **Riešenie**: Rozš<PERSON>rené hľadanie VecNormalize súborov s lepšou diagnostikou

### 2. Orderbook Volume Drift
- **Problém**: Orderbook volume features mali veľké kolísanie medzi dňami s rôznym objemom
- **Prejav**: V dňoch s vysokým objemom boli prahy ďaleko od nuly, po pokojnom dni zasa preskočili
- **Riešenie**: Nahradenie EMA smoothingu robustnou normalizáciou

## Implementované opravy

### 1. Vylepšené načítanie VecNormalize
```python
# Rozšírené hľadanie VecNormalize súborov
vecnorm_candidates = [
    model_path.with_suffix('.vecnorm.pkl'),
    model_path.parent / f"{model_stem}.vecnorm.pkl",
    # + všetky *.vecnorm.pkl súbory v adresári
]
```

**Výhody:**
- Automatické nájdenie VecNormalize súboru aj pri rôznych názvových konvenciách
- Detailná diagnostika ak sa súbor nenájde
- Varovanie o možnom drifte ak VecNormalize chýba

### 2. Robustná normalizácia orderbook volumes
```python
def apply_orderbook_volume_normalization(df, previous_stats=None, alpha=0.1):
    # Nahradenie EMA smoothingu normalizáciou: (x - mean) / std
    # Clipping na rozumný rozsah: [-5, 5]
    # Running statistics s EMA pre stabilitu
```

**Výhody:**
- Eliminuje drift medzi dňami s rôznym objemom
- Stabilnejšie ako EMA smoothing
- Zachováva relatívne rozdiely medzi orderbook úrovňami

### 3. Rozšírená diagnostika
- **VecNormalize status**: Kontrola načítania a aplikácie
- **Entry signal statistics**: Monitoring distribúcie signálov
- **Threshold breach rates**: Kontrola rozumnosti prahov
- **Drift detection**: Varovania pri extrémnych hodnotách

## Použitie

### 1. Test opráv
```bash
python test_threshold_stability.py
```

### 2. Spustenie simulácie s diagnostikou
```bash
python simulate_trading_new.py
```

### 3. Kontrola logov
Hľadajte tieto správy:
- ✅ "VecNormalize úspešne načítané" 
- ✅ "Applied orderbook volume normalization"
- ⚠️ Varovania o drifte alebo extrémnych hodnotách

## Očakávané výsledky

### Pred opravou:
- Prahy kolísali od 0.3 do 0.7+
- Entry signály mali nestabilnú distribúciu
- Rozdielne výsledky medzi dňami

### Po oprave:
- Stabilnejšie prahy okolo konfiguračných hodnôt
- Konzistentná distribúcia entry signálov
- Lepšia reprodukovateľnosť výsledkov

## Monitoring

Po implementácii sledujte:

1. **VecNormalize loading**: Či sa načítava bez chýb
2. **Entry signal stats**: Priemer okolo 0, std v rozumnom rozsahu (0.1-2.0)
3. **Threshold breach rate**: 1-50% signálov by malo prekračovať prahy
4. **Orderbook volume stats**: Normalizované hodnoty v rozsahu [-5, 5]

## Ďalšie kroky

Ak problémy pretrvávajú:
1. Skontrolujte, či existuje správny VecNormalize súbor
2. Overte, že orderbook features sú v dátach
3. Zvážte úpravu konfiguračných prahov
4. Analyzujte distribúciu features pred a po normalizácii
