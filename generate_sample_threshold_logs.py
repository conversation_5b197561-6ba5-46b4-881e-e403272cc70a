#!/usr/bin/env python3
"""
Generuje sample threshold logy podobné threshold_logs_sample.txt
Používa simulované dáta namiesto parquet súborov
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import random

# Setup logging to match the sample format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
log = logging.getLogger(__name__)

def generate_sample_threshold_logs():
    """Generate sample threshold logs similar to threshold_logs_sample.txt"""
    
    # Configuration matching the sample
    base_long_threshold = 0.6
    base_short_threshold = 0.6
    base_exit_threshold = 0.6
    enable_dynamic = True
    volatility_factor = 0.15
    
    # Log initial configuration
    log.info(f"Entry thresholds - Long: {base_long_threshold}, Short: {base_short_threshold}, Exit: {base_exit_threshold}")
    log.info(f"Dynamic thresholds enabled: {enable_dynamic}")
    
    # Generate simulated data for one day
    start_time = datetime(2025, 7, 5, 0, 0, 0)
    end_time = datetime(2025, 7, 5, 23, 59, 59)
    
    # Generate timestamps every few seconds (similar to sample)
    timestamps = []
    current_time = start_time
    while current_time <= end_time:
        timestamps.append(current_time)
        # Random interval between 1-300 seconds to simulate realistic trading
        interval = random.randint(1, 300)
        current_time += timedelta(seconds=interval)
    
    log.info(f"Generating threshold logs for {len(timestamps)} time points")
    
    threshold_count = 0
    
    # Generate realistic entry signals and thresholds
    for timestamp in timestamps:
        # Generate realistic entry signal (-1 to 1)
        entry_sig = random.uniform(-1.0, 1.0)
        
        # Generate realistic ATR values
        current_atr = random.uniform(0.001, 0.01)  # Typical ATR for crypto
        avg_atr = random.uniform(0.002, 0.008)     # Average ATR
        
        # Calculate dynamic thresholds (similar to the logic in sample)
        if enable_dynamic and avg_atr > 0:
            volatility_ratio = current_atr / avg_atr
            
            # Adjust thresholds based on volatility
            if volatility_ratio > 1.2:  # High volatility - easier entry
                long_adjustment = -volatility_factor * (volatility_ratio - 1.0)
                short_adjustment = -volatility_factor * (volatility_ratio - 1.0)
            elif volatility_ratio < 0.8:  # Low volatility - harder entry
                long_adjustment = volatility_factor * (1.0 - volatility_ratio)
                short_adjustment = volatility_factor * (1.0 - volatility_ratio)
            else:  # Normal volatility
                long_adjustment = 0.0
                short_adjustment = 0.0
            
            dynamic_long_threshold = max(0.3, min(0.9, base_long_threshold + long_adjustment))
            dynamic_short_threshold = max(0.3, min(0.9, base_short_threshold + short_adjustment))
        else:
            dynamic_long_threshold = base_long_threshold
            dynamic_short_threshold = base_short_threshold
        
        # Check for threshold exceedance (only log when thresholds are exceeded)
        long_exceeded = entry_sig > dynamic_long_threshold
        short_exceeded = entry_sig < -dynamic_short_threshold
        
        if long_exceeded:
            log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                    f"LONG ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} > {dynamic_long_threshold:.12f}")
            threshold_count += 1
        
        elif short_exceeded:
            log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                    f"SHORT ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} < -{dynamic_short_threshold:.12f}")
            threshold_count += 1
        
        # Occasionally log exit threshold exceeded (less frequent)
        if random.random() < 0.05:  # 5% chance
            exit_sig = random.uniform(-0.8, 0.8)
            if abs(exit_sig) > base_exit_threshold:
                log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                        f"EXIT THRESHOLD EXCEEDED: {exit_sig:.4f} (thresh {base_exit_threshold:.12f})")
        
        # Occasionally log dynamic threshold debug info (very rare)
        if random.random() < 0.01:  # 1% chance
            log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00: "
                    f"Dynamic thresholds: Long={dynamic_long_threshold:.12f}, "
                    f"Short={dynamic_short_threshold:.12f}, Exit={base_exit_threshold:.12f} "
                    f"(ATR: {current_atr:.6f}, AvgATR: {avg_atr:.6f})")
    
    log.info(f"Generated {threshold_count} threshold exceedances.")

def main():
    """Main function"""
    print("🎯 Generating sample threshold logs...")
    print("📋 This will create logs similar to threshold_logs_sample.txt")
    print("💡 You can redirect output to a file: python generate_sample_threshold_logs.py > my_threshold_logs.txt")
    print()
    
    generate_sample_threshold_logs()
    
    print()
    print("✅ Sample threshold logs generated!")
    print("📊 To visualize these logs, use: python visualize_thresholds.py <log_file>")

if __name__ == "__main__":
    main()
