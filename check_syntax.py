#!/usr/bin/env python3
"""
Jednoduchá syntax kontrola pre opravený simulate_trading_new.py
"""

import ast
import sys

def check_syntax(filename):
    """Check if Python file has valid syntax"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse the AST to check syntax
        ast.parse(source, filename=filename)
        print(f"✅ {filename}: Syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename}: Syntax error at line {e.lineno}: {e.msg}")
        return False
    except FileNotFoundError:
        print(f"❌ {filename}: File not found")
        return False
    except Exception as e:
        print(f"❌ {filename}: Error checking syntax: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Checking syntax of modified files...")
    
    files_to_check = [
        'simulate_trading_new.py',
        'test_threshold_stability.py'
    ]
    
    all_valid = True
    for filename in files_to_check:
        if not check_syntax(filename):
            all_valid = False
    
    if all_valid:
        print("\n✅ All files have valid syntax!")
        print("\n📋 Summary of fixes implemented:")
        print("   1. ✅ VecNormalize loading improvements")
        print("   2. ✅ Orderbook volume normalization (replaces EMA smoothing)")
        print("   3. ✅ Enhanced diagnostics for drift detection")
        print("   4. ✅ Better error handling and warnings")
        print("\n🚀 Ready to test with real data!")
        print("   Run: python3 simulate_trading_new.py")
        return 0
    else:
        print("\n❌ Some files have syntax errors. Please fix them before proceeding.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
