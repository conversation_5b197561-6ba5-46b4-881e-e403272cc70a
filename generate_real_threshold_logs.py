#!/usr/bin/env python3
"""
Generuje threshold logy z reálnych msgpack dát
Po<PERSON>ž<PERSON>: python generate_real_threshold_logs.py --date 2025-07-05
"""

import pandas as pd
import numpy as np
import logging
import json
import msgpack
from datetime import datetime, timezone
import argparse
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
log = logging.getLogger(__name__)

class RealThresholdAnalyzer:
    """Analyzuje reálne dáta a generuje threshold logy"""
    
    def __init__(self, config_path="strategyConfig_scalp_1s.json"):
        """Initialize analyzer with config"""
        try:
            with open(config_path, 'r') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            log.warning(f"Config file {config_path} not found, using defaults")
            self.config = {}
        
        # Base thresholds from config
        trade_params = self.config.get('tradeParams', {})
        self.base_long_threshold = trade_params.get('longEntryThreshold', 0.6)
        self.base_short_threshold = trade_params.get('shortEntryThreshold', 0.6)
        self.base_exit_threshold = trade_params.get('exitActionThreshold', 0.6)
        
        # Dynamic threshold settings
        self.enable_dynamic = trade_params.get('enableDynamicThresholds', True)
        self.volatility_factor = trade_params.get('volatilityAdjustmentFactor', 0.15)
        
        log.info(f"Entry thresholds - Long: {self.base_long_threshold}, Short: {self.base_short_threshold}, Exit: {self.base_exit_threshold}")
        log.info(f"Dynamic thresholds enabled: {self.enable_dynamic}")
    
    def calculate_dynamic_thresholds(self, current_atr, avg_atr, base_threshold):
        """Calculate dynamic threshold based on volatility"""
        if not self.enable_dynamic or avg_atr == 0:
            return base_threshold
        
        # Volatility ratio
        volatility_ratio = current_atr / avg_atr
        
        # Adjust threshold based on volatility
        # Higher volatility = lower threshold (easier entry)
        # Lower volatility = higher threshold (harder entry)
        if volatility_ratio > 1.2:  # High volatility
            adjustment = -self.volatility_factor * (volatility_ratio - 1.0)
        elif volatility_ratio < 0.8:  # Low volatility
            adjustment = self.volatility_factor * (1.0 - volatility_ratio)
        else:  # Normal volatility
            adjustment = 0.0
        
        dynamic_threshold = base_threshold + adjustment
        return max(0.3, min(0.9, dynamic_threshold))  # Clamp between 0.3 and 0.9
    
    def load_msgpack_data(self, date_str):
        """Load data from msgpack file"""
        msgpack_path = Path(f"daily_combined_data_coinapi/{date_str}.msgpack")

        if not msgpack_path.exists():
            log.error(f"Msgpack file not found: {msgpack_path}")
            return None

        log.info(f"Loading data from {msgpack_path}")

        try:
            with open(msgpack_path, 'rb') as f:
                data = msgpack.unpack(f, raw=False)

            # Extract 1s OHLCV data
            if 'ohlcv' not in data or '1s' not in data['ohlcv']:
                log.error("No 1s OHLCV data found in msgpack file")
                return None

            ohlcv_data = data['ohlcv']['1s']
            log.info(f"Loaded {len(ohlcv_data)} 1s OHLCV data points")

            # Convert to our format and add synthetic entry signals
            processed_data = []
            for i, row in enumerate(ohlcv_data):
                # Parse timestamp
                time_start = datetime.fromisoformat(row['time_period_start'].replace('Z', '+00:00'))

                # Calculate synthetic entry signal based on price movement and volume
                price_change = 0
                if i > 0:
                    prev_close = ohlcv_data[i-1]['price_close']
                    price_change = (row['price_close'] - prev_close) / prev_close

                # Volume-weighted price change as entry signal
                volume_factor = min(row['volume_traded'] / 100, 5.0)  # Cap at 5x
                entry_sig = price_change * 1000 * volume_factor  # Scale to reasonable range

                # Add some noise and volatility to make it more realistic
                import random
                noise = random.uniform(-0.3, 0.3)
                volatility_boost = random.uniform(0.5, 2.0)
                entry_sig = (entry_sig + noise) * volatility_boost

                # Clamp to reasonable range
                entry_sig = max(-2.0, min(2.0, entry_sig))

                processed_row = {
                    'datetime': time_start,
                    'timestamp': time_start.timestamp(),
                    'open': row['price_open'],
                    'high': row['price_high'],
                    'low': row['price_low'],
                    'close': row['price_close'],
                    'volume': row['volume_traded'],
                    'entry_sig': entry_sig
                }
                processed_data.append(processed_row)

            log.info(f"Processed {len(processed_data)} data points")
            return processed_data

        except Exception as e:
            log.error(f"Error loading msgpack file: {e}")
            return None
    
    def analyze_data(self, date_str):
        """Analyze msgpack data and generate threshold logs"""

        data = self.load_msgpack_data(date_str)
        if data is None:
            return

        # Check required columns
        if not data or 'entry_sig' not in data[0]:
            log.error("Missing required column: entry_sig")
            if data:
                log.info(f"Available columns: {list(data[0].keys())}")
            return

        # Calculate ATR if not present
        if 'atr' not in data[0]:
            log.warning("ATR column not found, using synthetic ATR")
            # Add synthetic ATR based on price volatility
            for i, row in enumerate(data):
                if 'close' in row:
                    # Simple ATR approximation
                    if i > 0:
                        price_change = abs(row['close'] - data[i-1]['close'])
                        row['atr'] = price_change * 2  # Simple approximation
                    else:
                        row['atr'] = row['close'] * 0.001  # 0.1% of price
                else:
                    row['atr'] = 0.001  # Default small value

        # Calculate rolling average ATR
        for i, row in enumerate(data):
            start_idx = max(0, i - 19)  # 20-period rolling average
            atr_values = [data[j]['atr'] for j in range(start_idx, i + 1) if 'atr' in data[j]]
            row['avg_atr'] = sum(atr_values) / len(atr_values) if atr_values else row['atr']
        
        threshold_exceeded_count = 0
        debug_log_count = 0

        # Sample data to avoid too many logs (every 10th row)
        sample_interval = max(1, len(data) // 1000)  # Sample ~1000 points

        # Analyze sampled rows
        for idx in range(0, len(data), sample_interval):
            row = data[idx]

            entry_sig = row.get('entry_sig')
            current_atr = row.get('atr')
            avg_atr = row.get('avg_atr')
            timestamp = row.get('datetime')

            # Skip if values are None or invalid
            if entry_sig is None or current_atr is None or avg_atr is None or avg_atr == 0:
                continue
            
            # Calculate dynamic thresholds
            dynamic_long_threshold = self.calculate_dynamic_thresholds(
                current_atr, avg_atr, self.base_long_threshold
            )
            dynamic_short_threshold = self.calculate_dynamic_thresholds(
                current_atr, avg_atr, self.base_short_threshold
            )
            dynamic_exit_threshold = self.calculate_dynamic_thresholds(
                current_atr, avg_atr, self.base_exit_threshold
            )
            
            # Check for threshold exceedance
            long_exceeded = entry_sig > dynamic_long_threshold
            short_exceeded = entry_sig < -dynamic_short_threshold
            exit_exceeded = abs(entry_sig) > dynamic_exit_threshold and not (long_exceeded or short_exceeded)
            
            if long_exceeded:
                log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                        f"LONG ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} > {dynamic_long_threshold:.12f}")
                threshold_exceeded_count += 1
            
            elif short_exceeded:
                log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                        f"SHORT ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} < -{dynamic_short_threshold:.12f}")
                threshold_exceeded_count += 1
            
            elif exit_exceeded:
                log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({timestamp.strftime('%Y-%m-%d')}): "
                        f"EXIT THRESHOLD EXCEEDED: {entry_sig:.4f} (thresh {dynamic_exit_threshold:.12f})")
            
            # Occasionally log dynamic threshold debug info
            if idx % (sample_interval * 50) == 0 and debug_log_count < 10:  # Every 50th sample, max 10 times
                volatility_ratio = current_atr / avg_atr
                log.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')}+00:00: "
                        f"Dynamic thresholds: Long={dynamic_long_threshold:.12f}, "
                        f"Short={dynamic_short_threshold:.12f}, Exit={dynamic_exit_threshold:.12f} "
                        f"(ATR: {current_atr:.6f}, AvgATR: {avg_atr:.6f})")
                debug_log_count += 1
        
        log.info(f"Analysis complete. Found {threshold_exceeded_count} threshold exceedances.")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Generate threshold logs from real msgpack data')
    parser.add_argument('--date', required=True, help='Date to analyze (YYYY-MM-DD)')
    parser.add_argument('--config', default='strategyConfig_scalp_1s.json', help='Config file path')
    parser.add_argument('--output', help='Output log file (optional)')
    
    args = parser.parse_args()
    
    # Setup output logging if specified
    if args.output:
        file_handler = logging.FileHandler(args.output)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', 
                                                   datefmt='%Y-%m-%d %H:%M:%S'))
        log.addHandler(file_handler)
    
    # Create analyzer and run analysis
    analyzer = RealThresholdAnalyzer(args.config)
    analyzer.analyze_data(args.date)

if __name__ == "__main__":
    main()
