#!/usr/bin/env python3
"""
Test script pre overenie funkcionality EMA smoothing orderbook objemových d<PERSON>t.
"""
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def apply_orderbook_volume_smoothing(df: pd.DataFrame, previous_values: pd.Series = None, alpha: float = 0.3) -> pd.DataFrame:
    """
    Apply EMA smoothing to orderbook volume columns to reduce volatility in neural signals.
    
    Args:
        df: DataFrame with features including orderbook volume columns
        previous_values: Previous row values for EMA calculation
        alpha: EMA smoothing factor (0.1-0.5 range, lower = more smoothing)
    
    Returns:
        DataFrame with smoothed orderbook volume columns
    """
    if df.empty or previous_values is None:
        return df
    
    # Identify orderbook volume columns
    ob_vol_cols = [col for col in df.columns if col.startswith(('ob_bid_vol', 'ob_ask_vol'))]
    
    if not ob_vol_cols:
        return df
    
    # Apply EMA smoothing only to the latest row
    df_smoothed = df.copy()
    if len(df_smoothed) > 0:
        current_row = df_smoothed.iloc[-1].copy()
        
        smoothed_count = 0
        for col in ob_vol_cols:
            if col in current_row.index and col in previous_values.index:
                if not pd.isna(current_row[col]) and not pd.isna(previous_values[col]):
                    # Apply EMA: new = alpha * current + (1-alpha) * previous
                    smoothed_value = alpha * current_row[col] + (1 - alpha) * previous_values[col]
                    current_row[col] = smoothed_value
                    smoothed_count += 1
        
        # Update the DataFrame with smoothed values
        df_smoothed.iloc[-1] = current_row
        
        if smoothed_count > 0:
            print(f"📊 Applied EMA smoothing to {smoothed_count} orderbook volume columns")
    
    return df_smoothed

def generate_test_data(n_rows=100):
    """Generuje testové dáta s volatilnými orderbook objemami."""
    np.random.seed(42)
    
    # Základné OHLCV dáta
    data = {
        'open': np.random.uniform(1.0, 2.0, n_rows),
        'high': np.random.uniform(1.0, 2.0, n_rows),
        'low': np.random.uniform(1.0, 2.0, n_rows),
        'close': np.random.uniform(1.0, 2.0, n_rows),
        'volume': np.random.uniform(1000, 5000, n_rows),
    }
    
    # Volatilné orderbook objemy s náhodnými špičkami
    for level in range(1, 6):
        # Pridáme náhodné špičky (simulujeme volatilné orderbook dáta)
        base_vol = np.random.uniform(100, 1000, n_rows)
        spikes = np.random.choice([0, 1], n_rows, p=[0.9, 0.1])  # 10% šanca na špičku
        spike_multiplier = np.where(spikes, np.random.uniform(5, 20, n_rows), 1)
        
        data[f'ob_bid_vol_l{level}'] = base_vol * spike_multiplier
        data[f'ob_ask_vol_l{level}'] = base_vol * spike_multiplier * np.random.uniform(0.8, 1.2, n_rows)
    
    # Pridáme ďalšie features
    data['ATR_14'] = np.random.uniform(0.01, 0.05, n_rows)
    data['RSI_14'] = np.random.uniform(20, 80, n_rows)
    
    return pd.DataFrame(data)

def test_smoothing():
    """Test EMA smoothing funkcionality."""
    print("🧪 Generujem testové dáta s volatilnými orderbook objemami...")
    df = generate_test_data(100)
    
    print(f"📊 Pôvodné dáta: {df.shape}")
    print(f"📊 Orderbook volume stĺpce: {[col for col in df.columns if col.startswith(('ob_bid_vol', 'ob_ask_vol'))]}")
    
    # Aplikuj smoothing row by row (simuluje live trading)
    df_smoothed = df.copy()
    alpha = 0.3
    
    print(f"\n🔧 Aplikujem EMA smoothing s alpha={alpha}...")
    
    for i in range(1, len(df_smoothed)):
        current_df = df_smoothed.iloc[[i]].copy()
        previous_values = df_smoothed.iloc[i-1]
        
        smoothed_df = apply_orderbook_volume_smoothing(
            current_df,
            previous_values=previous_values,
            alpha=alpha
        )
        df_smoothed.iloc[i] = smoothed_df.iloc[0]
    
    # Porovnaj volatilitu pred a po smoothing
    ob_vol_cols = [col for col in df.columns if col.startswith(('ob_bid_vol', 'ob_ask_vol'))]
    
    print(f"\n📈 Analýza volatility pre {len(ob_vol_cols)} orderbook volume stĺpce:")
    
    for col in ob_vol_cols[:3]:  # Ukáž len prvé 3 stĺpce
        original_std = df[col].std()
        smoothed_std = df_smoothed[col].std()
        reduction = (original_std - smoothed_std) / original_std * 100
        
        print(f"  {col}:")
        print(f"    Pôvodná štandardná odchýlka: {original_std:.2f}")
        print(f"    Vyhladená štandardná odchýlka: {smoothed_std:.2f}")
        print(f"    Redukcia volatility: {reduction:.1f}%")
    
    # Vytvor graf pre vizualizáciu
    fig, axes = plt.subplots(2, 1, figsize=(12, 8))
    
    # Graf prvého orderbook volume stĺpca
    col = ob_vol_cols[0]
    axes[0].plot(df[col], label='Pôvodné', alpha=0.7, linewidth=1)
    axes[0].plot(df_smoothed[col], label='Vyhladené (EMA)', alpha=0.9, linewidth=2)
    axes[0].set_title(f'Porovnanie volatility: {col}')
    axes[0].set_ylabel('Objem')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Graf druhého orderbook volume stĺpca
    col = ob_vol_cols[1]
    axes[1].plot(df[col], label='Pôvodné', alpha=0.7, linewidth=1)
    axes[1].plot(df_smoothed[col], label='Vyhladené (EMA)', alpha=0.9, linewidth=2)
    axes[1].set_title(f'Porovnanie volatility: {col}')
    axes[1].set_xlabel('Časový index')
    axes[1].set_ylabel('Objem')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('orderbook_smoothing_test.png', dpi=150, bbox_inches='tight')
    print(f"\n📊 Graf uložený ako 'orderbook_smoothing_test.png'")
    
    return df, df_smoothed

if __name__ == "__main__":
    print("🚀 Spúšťam test EMA smoothing pre orderbook objemové dáta...")
    original_df, smoothed_df = test_smoothing()
    print("\n✅ Test dokončený!")
