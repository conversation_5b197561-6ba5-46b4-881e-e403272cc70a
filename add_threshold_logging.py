#!/usr/bin/env python3
"""
Skript na pridanie threshold loggingu do existujúcich simulačných skriptov
"""

import re
from pathlib import Path

def add_threshold_logging_to_simulate_trading():
    """Add threshold logging to simulate_trading_new.py"""
    
    script_path = Path("simulate_trading_new.py")
    if not script_path.exists():
        print(f"❌ File {script_path} not found")
        return False
    
    # Read the original file
    with open(script_path, 'r') as f:
        content = f.read()
    
    # Check if threshold logging is already added
    if "ENTRY THRESHOLD EXCEEDED" in content:
        print("✅ Threshold logging already exists in simulate_trading_new.py")
        return True
    
    # Find the section where entry signals are evaluated
    # Look for the pattern where triggered_pos is set
    pattern = r'(triggered_pos = 0\s*\n\s*if entry_sig > long_entry_thr: triggered_pos = 1\s*\n\s*elif entry_sig < -short_entry_thr: triggered_pos = -1)'
    
    replacement = '''triggered_pos = 0
                if entry_sig > long_entry_thr: 
                    triggered_pos = 1
                    # Log threshold exceedance
                    log.info(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({current_time.strftime('%Y-%m-%d')}): "
                            f"LONG ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} > {long_entry_thr:.12f}")
                elif entry_sig < -short_entry_thr: 
                    triggered_pos = -1
                    # Log threshold exceedance
                    log.info(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({current_time.strftime('%Y-%m-%d')}): "
                            f"SHORT ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} < -{short_entry_thr:.12f}")'''
    
    # Apply the replacement
    new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    if new_content == content:
        print("❌ Could not find the pattern to replace in simulate_trading_new.py")
        print("Manual modification may be required")
        return False
    
    # Create backup
    backup_path = script_path.with_suffix('.py.backup')
    with open(backup_path, 'w') as f:
        f.write(content)
    print(f"✅ Backup created: {backup_path}")
    
    # Write modified content
    with open(script_path, 'w') as f:
        f.write(new_content)
    
    print("✅ Threshold logging added to simulate_trading_new.py")
    return True

def create_threshold_logging_patch():
    """Create a patch that can be manually applied"""
    
    patch_content = '''
# Add this code after the line where triggered_pos is evaluated:

# Original code:
# triggered_pos = 0
# if entry_sig > long_entry_thr: triggered_pos = 1
# elif entry_sig < -short_entry_thr: triggered_pos = -1

# Replace with:
triggered_pos = 0
if entry_sig > long_entry_thr: 
    triggered_pos = 1
    # Log threshold exceedance
    log.info(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({current_time.strftime('%Y-%m-%d')}): "
            f"LONG ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} > {long_entry_thr:.12f}")
elif entry_sig < -short_entry_thr: 
    triggered_pos = -1
    # Log threshold exceedance
    log.info(f"{current_time.strftime('%Y-%m-%d %H:%M:%S')}+00:00 ({current_time.strftime('%Y-%m-%d')}): "
            f"SHORT ENTRY THRESHOLD EXCEEDED: {entry_sig:.4f} < -{short_entry_thr:.12f}")

# Also add at the beginning of the simulation function:
log.info(f"Entry thresholds - Long: {long_entry_thr}, Short: {short_entry_thr}, Exit: {exit_thr}")
log.info(f"Dynamic thresholds enabled: {dynamic_thresholds_enabled}")
'''
    
    with open("threshold_logging_patch.txt", "w") as f:
        f.write(patch_content)
    
    print("✅ Created threshold_logging_patch.txt with manual instructions")

def main():
    """Main function"""
    print("🔧 Adding threshold logging to simulation scripts...")
    
    # Try automatic modification
    success = add_threshold_logging_to_simulate_trading()
    
    if not success:
        print("\n📝 Creating manual patch instructions...")
        create_threshold_logging_patch()
    
    print("\n📋 Usage instructions:")
    print("1. Run your simulation as usual:")
    print("   python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-07-05 --end 2025-07-05")
    print("\n2. To save logs to file:")
    print("   python simulate_trading_new.py --cfg strategyConfig_scalp_1s.json --start 2025-07-05 --end 2025-07-05 > my_threshold_logs.txt")
    print("\n3. To visualize the logs:")
    print("   python visualize_thresholds.py my_threshold_logs.txt")

if __name__ == "__main__":
    main()
