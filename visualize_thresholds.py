#!/usr/bin/env python3
"""
Script na vizualizáciu dynamických thresholdov z logov simulácie
"""

import re
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np
import sys

def parse_threshold_logs(log_file_or_text):
    """Parse threshold exceedance logs from simulation output"""
    
    # Patterns for different log types (updated to match our log format)
    patterns = {
        'long_entry': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2} \(\d{4}-\d{2}-\d{2}\): LONG ENTRY THRESHOLD EXCEEDED: ([\d\.-]+) > ([\d\.-]+)',
        'short_entry': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2} \(\d{4}-\d{2}-\d{2}\): SHORT ENTRY THRESHOLD EXCEEDED: ([\d\.-]+) < -([\d\.-]+)',
        'exit': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2} \(\d{4}-\d{2}-\d{2}\): EXIT THRESHOLD EXCEEDED: ([\d\.-]+) \(thresh ([\d\.-]+)\)',
        'dynamic_debug': r'\[INFO\] (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\+\d{2}:\d{2}: Dynamic thresholds: Long=([\d\.-]+), Short=([\d\.-]+), Exit=([\d\.-]+) \(ATR: ([\d\.-]+), AvgATR: ([\d\.-]+)\)'
    }
    
    data = []
    
    # Read log content
    if isinstance(log_file_or_text, str) and '\n' in log_file_or_text:
        log_content = log_file_or_text
    else:
        try:
            with open(log_file_or_text, 'r') as f:
                log_content = f.read()
        except:
            print(f"Could not read file: {log_file_or_text}")
            return pd.DataFrame()
    
    lines = log_content.split('\n')
    
    for line in lines:
        # Parse long entry thresholds
        match = re.search(patterns['long_entry'], line)
        if match:
            timestamp = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
            signal = float(match.group(2))
            threshold = float(match.group(3))
            data.append({
                'timestamp': timestamp,
                'type': 'long_entry',
                'signal': signal,
                'threshold': threshold,
                'exceeded': True
            })
        
        # Parse short entry thresholds
        match = re.search(patterns['short_entry'], line)
        if match:
            timestamp = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
            signal = float(match.group(2))
            threshold = float(match.group(3))
            data.append({
                'timestamp': timestamp,
                'type': 'short_entry',
                'signal': signal,
                'threshold': threshold,
                'exceeded': True
            })
        
        # Parse exit thresholds
        match = re.search(patterns['exit'], line)
        if match:
            timestamp = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
            signal = float(match.group(2))
            threshold = float(match.group(3))
            data.append({
                'timestamp': timestamp,
                'type': 'exit',
                'signal': signal,
                'threshold': threshold,
                'exceeded': True
            })
        
        # Parse dynamic threshold debug logs
        match = re.search(patterns['dynamic_debug'], line)
        if match:
            timestamp = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
            long_thr = float(match.group(2))
            short_thr = float(match.group(3))
            exit_thr = float(match.group(4))
            current_atr = float(match.group(5))
            avg_atr = float(match.group(6))
            
            data.append({
                'timestamp': timestamp,
                'type': 'dynamic_long',
                'threshold': long_thr,
                'current_atr': current_atr,
                'avg_atr': avg_atr,
                'volatility_ratio': current_atr / avg_atr if avg_atr > 0 else 1.0
            })
            data.append({
                'timestamp': timestamp,
                'type': 'dynamic_short',
                'threshold': short_thr,
                'current_atr': current_atr,
                'avg_atr': avg_atr,
                'volatility_ratio': current_atr / avg_atr if avg_atr > 0 else 1.0
            })
            data.append({
                'timestamp': timestamp,
                'type': 'dynamic_exit',
                'threshold': exit_thr,
                'current_atr': current_atr,
                'avg_atr': avg_atr,
                'volatility_ratio': current_atr / avg_atr if avg_atr > 0 else 1.0
            })
    
    return pd.DataFrame(data)

def plot_threshold_evolution(df, save_path='threshold_evolution.png'):
    """Plot how thresholds change over time"""
    
    if df.empty:
        print("No threshold data found in logs")
        return
    
    fig, axes = plt.subplots(4, 1, figsize=(15, 12))
    fig.suptitle('Dynamic Threshold Evolution During Trading', fontsize=16)
    
    # Filter dynamic threshold data
    dynamic_data = df[df['type'].str.startswith('dynamic_')]
    
    if not dynamic_data.empty:
        # Plot 1: Long Entry Thresholds
        long_data = dynamic_data[dynamic_data['type'] == 'dynamic_long']
        if not long_data.empty:
            axes[0].plot(long_data['timestamp'], long_data['threshold'], 'g-', alpha=0.7, label='Long Entry Threshold')
            axes[0].set_ylabel('Long Entry Threshold')
            axes[0].set_title('Long Entry Threshold Evolution')
            axes[0].grid(True, alpha=0.3)
            axes[0].legend()
        
        # Plot 2: Short Entry Thresholds
        short_data = dynamic_data[dynamic_data['type'] == 'dynamic_short']
        if not short_data.empty:
            axes[1].plot(short_data['timestamp'], short_data['threshold'], 'r-', alpha=0.7, label='Short Entry Threshold')
            axes[1].set_ylabel('Short Entry Threshold')
            axes[1].set_title('Short Entry Threshold Evolution')
            axes[1].grid(True, alpha=0.3)
            axes[1].legend()
        
        # Plot 3: Exit Thresholds
        exit_data = dynamic_data[dynamic_data['type'] == 'dynamic_exit']
        if not exit_data.empty:
            axes[2].plot(exit_data['timestamp'], exit_data['threshold'], 'b-', alpha=0.7, label='Exit Threshold')
            axes[2].set_ylabel('Exit Threshold')
            axes[2].set_title('Exit Threshold Evolution')
            axes[2].grid(True, alpha=0.3)
            axes[2].legend()
        
        # Plot 4: Volatility Ratio (ATR current vs average)
        if not dynamic_data.empty and 'volatility_ratio' in dynamic_data.columns:
            vol_data = dynamic_data[dynamic_data['type'] == 'dynamic_long'].copy()  # Use any type, they have same volatility data
            if not vol_data.empty:
                axes[3].plot(vol_data['timestamp'], vol_data['volatility_ratio'], 'purple', alpha=0.7, label='Volatility Ratio (Current/Avg ATR)')
                axes[3].axhline(y=1.0, color='black', linestyle='--', alpha=0.5, label='Baseline (1.0)')
                axes[3].set_ylabel('Volatility Ratio')
                axes[3].set_title('Market Volatility Ratio (Current ATR / Average ATR)')
                axes[3].grid(True, alpha=0.3)
                axes[3].legend()
    
    # Format x-axis for all subplots
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Threshold evolution plot saved to: {save_path}")
    plt.show()

def plot_threshold_exceedances(df, save_path='threshold_exceedances.png'):
    """Plot threshold exceedances over time"""
    
    if df.empty:
        print("No threshold exceedance data found")
        return
    
    # Filter exceedance data
    exceedance_data = df[df.get('exceeded', False) == True]
    
    if exceedance_data.empty:
        print("No threshold exceedances found in data")
        return
    
    fig, ax = plt.subplots(figsize=(15, 8))
    
    # Plot exceedances by type
    for exc_type in ['long_entry', 'short_entry', 'exit']:
        type_data = exceedance_data[exceedance_data['type'] == exc_type]
        if not type_data.empty:
            color = {'long_entry': 'green', 'short_entry': 'red', 'exit': 'blue'}[exc_type]
            ax.scatter(type_data['timestamp'], type_data['signal'], 
                      c=color, alpha=0.6, s=30, label=f'{exc_type.replace("_", " ").title()} Exceedances')
    
    ax.set_xlabel('Time')
    ax.set_ylabel('Signal Value')
    ax.set_title('Threshold Exceedances Over Time')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # Format x-axis
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Threshold exceedances plot saved to: {save_path}")
    plt.show()

def analyze_threshold_stats(df):
    """Analyze threshold statistics"""
    
    if df.empty:
        print("No data to analyze")
        return
    
    print("\n" + "="*60)
    print("THRESHOLD ANALYSIS SUMMARY")
    print("="*60)
    
    # Dynamic threshold stats
    dynamic_data = df[df['type'].str.startswith('dynamic_')]
    if not dynamic_data.empty:
        print("\n📊 DYNAMIC THRESHOLD STATISTICS:")
        for thresh_type in ['dynamic_long', 'dynamic_short', 'dynamic_exit']:
            type_data = dynamic_data[dynamic_data['type'] == thresh_type]
            if not type_data.empty:
                print(f"\n{thresh_type.replace('dynamic_', '').upper()} THRESHOLDS:")
                print(f"  Min: {type_data['threshold'].min():.4f}")
                print(f"  Max: {type_data['threshold'].max():.4f}")
                print(f"  Mean: {type_data['threshold'].mean():.4f}")
                print(f"  Std: {type_data['threshold'].std():.4f}")
                print(f"  Range: {type_data['threshold'].max() - type_data['threshold'].min():.4f}")
    
    # Exceedance stats
    exceedance_data = df[df.get('exceeded', False) == True]
    if not exceedance_data.empty:
        print(f"\n🎯 THRESHOLD EXCEEDANCES:")
        for exc_type in ['long_entry', 'short_entry', 'exit']:
            type_count = len(exceedance_data[exceedance_data['type'] == exc_type])
            print(f"  {exc_type.replace('_', ' ').title()}: {type_count} times")
        
        print(f"\n  Total exceedances: {len(exceedance_data)}")
        
        # Time distribution
        if 'timestamp' in exceedance_data.columns:
            exceedance_data['hour'] = exceedance_data['timestamp'].dt.hour
            hourly_counts = exceedance_data['hour'].value_counts().sort_index()
            print(f"\n⏰ HOURLY DISTRIBUTION:")
            for hour, count in hourly_counts.items():
                print(f"  {hour:02d}:00 - {count} exceedances")

def main():
    """Main function"""
    
    # Check if log file provided as argument
    if len(sys.argv) > 1:
        log_source = sys.argv[1]
    else:
        # Try to read from stdin if available
        import select
        if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
            log_source = sys.stdin.read()
        else:
            print("Usage: python visualize_thresholds.py <log_file_or_pipe_input>")
            print("Example: python simulate_trading_new.py ... | python visualize_thresholds.py")
            return
    
    # Parse threshold data
    print("Parsing threshold data from logs...")
    df = parse_threshold_logs(log_source)
    
    if df.empty:
        print("No threshold data found in logs. Make sure dynamic thresholds are enabled and logging is working.")
        return
    
    print(f"Found {len(df)} threshold data points")
    
    # Generate visualizations
    plot_threshold_evolution(df)
    plot_threshold_exceedances(df)
    
    # Print analysis
    analyze_threshold_stats(df)

if __name__ == "__main__":
    main()
