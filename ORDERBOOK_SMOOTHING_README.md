# EMA Smoothing pre Orderbook Objemové Dáta

## Prehľad

Implementácia EMA (Exponential Moving Average) smoothing pre orderbook objemové dáta s cieľom redukovať volatilitu neurónových signálov a zabrániť falošným vstupom do obchodov na základe jednosekundových výkyvov likvidity.

## Problém

Analýza súboru `threshold_logs_sample.txt` uk<PERSON>zal<PERSON>, že signály z modelu sú príliš volatilné a reagujú na krátkodobé výkyvy v trhu. Orderbook objemové dáta (`ob_bid_vol_l1` až `ob_bid_vol_l5` a `ob_ask_vol_l1` až `ob_ask_vol_l5`) sa len logaritmicky transformovali, ale časovo sa nefiltrovali, čo spôsobovalo náhle výkyvy v entry signáloch (až k hodnotám ±0.9).

## Riešenie

### 1. Nová funkcia `apply_orderbook_volume_smoothing()`

```python
def apply_orderbook_volume_smoothing(df: pd.DataFrame, previous_values: pd.Series = None, alpha: float = 0.3) -> pd.DataFrame:
    """
    Apply EMA smoothing to orderbook volume columns to reduce volatility in neural signals.
    
    Args:
        df: DataFrame with features including orderbook volume columns
        previous_values: Previous row values for EMA calculation
        alpha: EMA smoothing factor (0.1-0.5 range, lower = more smoothing)
    
    Returns:
        DataFrame with smoothed orderbook volume columns
    """
```

### 2. Konfiguračné parametre

Pridané do všetkých konfiguračných súborov:

```json
{
  "orderbook_smoothing_enabled": true,
  "orderbook_smoothing_alpha": 0.3
}
```

- **orderbook_smoothing_enabled**: Zapína/vypína smoothing (default: true)
- **orderbook_smoothing_alpha**: EMA smoothing faktor (0.1-0.5, nižšie = viac smoothing, default: 0.3)

### 3. Integrácia do všetkých trading súborov

Smoothing sa aplikuje **PRED** logaritmickou transformáciou v týchto súboroch:

- `live_trading.py` - Live trading
- `simulate_live_trading.py` - Simulácia live trading logiky
- `simulate_live_trading_exact.py` - Presná simulácia live trading
- `simulate_trading_new.py` - Backtesting simulácia

## Výsledky testovania

Test na syntetických dátach s volatilnými orderbook objemami ukázal:

- **ob_bid_vol_l1**: 55.8% redukcia volatility
- **ob_ask_vol_l1**: 57.3% redukcia volatility
- **ob_bid_vol_l2**: 46.4% redukcia volatility

## Technické detaily

### EMA Formula
```
smoothed_value = alpha * current_value + (1 - alpha) * previous_value
```

### Identifikácia stĺpcov
Smoothing sa aplikuje len na stĺpce začínajúce s:
- `ob_bid_vol`
- `ob_ask_vol`

### Časovanie aplikácie
1. **Forward fill** na všetky features
2. **EMA smoothing** na orderbook volume stĺpce
3. **Log1p transformácia** na všetky volume stĺpce
4. Ďalšie spracovanie...

## Konfiguračné súbory aktualizované

- `strategyConfig_optimized.json`
- `strategyConfig_simple_optimized.json`
- `temp_config_1s.json`

## Očakávané výhody

1. **Stabilnejšie neurónové signály** - Redukcia "špičiek" v signáloch
2. **Menej falošných vstupov** - Zabránenie obchodom na základe jednosekundových výkyvov
3. **Lepšia konzistencia** - Medzi live trading a backtesting
4. **Konfigurovateľnosť** - Možnosť nastavenia smoothing parametrov

## Použitie

### Zapnutie/vypnutie smoothing
```json
"orderbook_smoothing_enabled": false
```

### Nastavenie smoothing faktora
```json
"orderbook_smoothing_alpha": 0.2  // Viac smoothing (menej volatilné)
"orderbook_smoothing_alpha": 0.5  // Menej smoothing (viac responsívne)
```

## Testovanie

Spustite test script pre overenie funkcionality:
```bash
python test_orderbook_smoothing.py
```

Test vytvorí graf `orderbook_smoothing_test.png` zobrazujący porovnanie pôvodných a vyhladených dát.
